'use client';

import { useSidebar } from "@/contexts/SidebarProvider"
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/Tabs";
import UserTable from "./components/UserTable";

export default function UserManagement() {
  const { setTitle } = useSidebar();
  const [activeTab, setActiveTab] = useState("clients");

  useEffect(() => {
    setTitle('User Management');
  }, []);

  return (
    <section className="p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-sm border">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="border-b px-6 py-4">
            <TabsList className="grid w-full grid-cols-2 max-w-md bg-gray-100">
              <TabsTrigger
                value="clients"
                className="data-[state=active]:bg-primary data-[state=active]:text-white"
              >
                Clients
              </TabsTrigger>
              <TabsTrigger
                value="customers"
                className="data-[state=active]:bg-primary data-[state=active]:text-white"
              >
                Customers
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="clients" className="p-6">
            <UserTable userType="Merchant" />
          </TabsContent>

          <TabsContent value="customers" className="p-6">
            <UserTable userType="Customer" />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  )
}

