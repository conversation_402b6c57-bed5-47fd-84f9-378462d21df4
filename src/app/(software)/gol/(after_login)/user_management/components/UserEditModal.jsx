'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import Input from '@/components/ui/Input';
import { Select, SelectItem } from '@/components/ui/Select';
import Button from '@/components/ui/Button';
import { useCollection } from '@/hooks/useCollection';
import { toast } from 'sonner';
import { X, Save } from 'lucide-react';

export default function UserEditModal({ user, onClose, onUpdate }) {
  const { updateItem } = useCollection('users');
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    firstname: '',
    lastname: '',
    username: '',
    email: '',
    phone: '',
    role: '',
    verified: false,
    status: '',
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        firstname: user.firstname || '',
        lastname: user.lastname || '',
        username: user.username || '',
        email: user.email || '',
        phone: user.phone || '',
        role: user.role || '',
        verified: user.verified || false
      });
    }
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await updateItem(user.id, {
        name: formData.name,
        firstname: formData.firstname,
        lastname: formData.lastname,
        username: formData.username,
        email: formData.email,
        phone: formData.phone ? Number(formData.phone) : null,
        role: formData.role,
        verified: formData.verified
      });

      toast.success('User updated successfully');
      onUpdate();
      onClose();
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    } finally {
      setIsLoading(false);
    }
  };

  const roleOptions = [
    { value: 'Customer', label: 'Customer' },
    { value: 'Merchant', label: 'Client' },
    { value: 'GOLStaff', label: 'GOL Staff' },
    { value: 'GOLMod', label: 'GOL Moderator' },
    { value: 'Root', label: 'Root Admin' }
  ];

  return (
    <Dialog
      open={true}
      onOpenChange={onClose}
      title="Edit User Information"
    >
      <form onSubmit={handleSubmit} className="space-y-4 w-full max-w-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Full Name</label>
              <Input
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Username</label>
              <Input
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter username"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">First Name</label>
              <Input
                name="firstname"
                value={formData.firstname}
                onChange={handleInputChange}
                placeholder="Enter first name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Last Name</label>
              <Input
                name="lastname"
                value={formData.lastname}
                onChange={handleInputChange}
                placeholder="Enter last name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <Input
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter email address"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Phone Number</label>
              <Input
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="Enter phone number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Role</label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleSelectChange('role', value)}
                placeholder="Select role"
              >
                {roleOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="verified"
                name="verified"
                checked={formData.verified}
                onChange={handleInputChange}
                className="rounded border-gray-300"
              />
              <label htmlFor="verified" className="text-sm font-medium">
                Account Verified/Active
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              title="Cancel"
              onClick={onClose}
              disabled={isLoading}
            />
            <Button
              type="submit"
              variant="default"
              title="Save Changes"
              icon={<Save size={18} />}
              disabled={isLoading}
            />
          </div>
        </form>
    </Dialog>
  );
}
